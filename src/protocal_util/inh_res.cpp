#include "inh_res.h"
#include <string.h>
using namespace cvis_bcl;

INHRes::INHRes()
{
    msg_type = "V2C_INH_RES";
}

INHRes::~INHRes()
{

}

bool INHRes::ParseProtocal(BYTE* buffer, int length)
{
    DWORD offset = 0;
    if(!header_.ParseHeader(buffer, offset, length))
    {
        return false;
    }
    if(header_.GetLength() + offset > length)
    {
        return false;
    }

    // 根据最新的 INHResData 结构体定义解析
    for(int i = 0; i < 8; i++)
    {
        data_.veh_id[i] = GetHostUnsignedChar(buffer, offset);
    }
    data_.res = GetHostUnsignedChar(buffer, offset);
    return true;
}

BYTE* INHRes::MakeINHResPackage(INHResData data, DWORD& length)
{
    // 使用 INHResData 的 GetLength 方法计算实际数据长度
    HeaderData header = MakeHeader(msg_type, data.GetLength());
    length = data.GetLength() + sizeof(header);
    buffer_ = (BYTE*)malloc(length);
    memset(buffer_, 0, length);
    uint32_t offset = 0;
    header_.MakeHeader(buffer_, offset, header);
    MakePureDataPackage(buffer_, offset, data);
    return buffer_;
}

void INHRes::MakePureDataPackage(BYTE* buffer, DWORD& offset, INHResData data)
{
    // 车辆编号 veh_id[8]
    for(int i = 0; i < 8; i++)
    {
        MakeUnsignedChar(buffer, offset, data.veh_id[i]);
    }

    // 响应结果 res
    MakeUnsignedChar(buffer, offset, data.res);
}

INHResData INHRes::GetData()
{
    return data_;
}