#include "state_resend_res.h"
#include <string.h>
using namespace cvis_bcl;

StateResendRes::StateResendRes()
{
    msg_type = "V2C_STATE_RESEND_RES";
}

StateResendRes::~StateResendRes()
{

}

BYTE* StateResendRes::MakeStateResendResPackage(StateResendResData data, DWORD& length)
{
    // 使用 StateResendResData 的 GetLength 方法计算实际数据长度
    HeaderData header = MakeHeader(msg_type, data.GetLength());
    length = data.GetLength() + sizeof(header);
    buffer_ = (BYTE*)malloc(length);
    memset(buffer_, 0, length);
    uint32_t offset = 0;
    header_.MakeHeader(buffer_, offset, header);
    MakePureDataPackage(buffer_, offset, data);
    return buffer_;
}

void StateResendRes::MakePureDataPackage(BYTE* buffer, DWORD& offset, StateResendResData data)
{
    // 补发消息序号 message_id
    MakeUnsignedShort(buffer, offset, data.message_id);
    
    // 补发状态 status
    MakeUnsignedChar(buffer, offset, data.status);
}
