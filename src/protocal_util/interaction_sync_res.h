/*---------------------------------------------------------------------
/* Copyright (c) 2023 tsari. All rights reserved. */
/**
* @Author: hlf
* @brief:云端接管交互指令返回协议类
 *       
 * @version: 版本编号  修订日期  修订者 修订内容 （最新的在最前面）
 * --------------------------------------------------------------------
*/
#pragma once
#include "protocal_base.h"
namespace cvis_bcl
{
    class InteractionSyncRes:public ProtocalBase
    {
    public:
        InteractionSyncRes();
        virtual ~InteractionSyncRes();
        BYTE* MakeProtocalPackage(InteractionSyncResData data,DWORD& length);
    };

}