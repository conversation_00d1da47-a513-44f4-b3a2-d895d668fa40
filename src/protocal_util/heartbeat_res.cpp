#include "heartbeat_res.h"
#include <iostream>
#include "protocal_map.hpp"
#include <string.h>
using namespace cvis_bcl;

HeartBeatRes::HeartBeatRes()
{
    msg_type = "HEARTBEAT_RES";
}

HeartBeatRes::~HeartBeatRes()
{

}

bool HeartBeatRes::ParseProtocal(BYTE* buffer,int length)
{
    DWORD offset=0;
    if(!header_.ParseHeader(buffer,offset,length))
    {
        return false;
    }
    if(header_.GetLength()+offset>length)
    {
        return false;
    }
    data_.msg_seq = GetHostUnsignedInt(buffer,offset);
    data_.timestamp = GetHostlonglong(buffer,offset);
    for(int i=0;i<8;i++)
    {
        data_.veh_id[i] =GetHostUnsignedChar(buffer,offset);
    }
    return true;
}


BYTE* HeartBeatRes::MakeHeartBeatResPackage(HeartBeatData data,DWORD& length)
{
    uint32_t offset =0;
    HeaderData header = MakeHeader(msg_type,sizeof(data));
    length = sizeof(data)+sizeof(header);
    
    buffer_ = (BYTE*)malloc(length);
    memset(buffer_,0,36);
    
    header_.MakeHeader(buffer_,offset,header);
    MakeUnsignedInt(buffer_,offset,data.msg_seq);
    MakeUsignedLongLong(buffer_,offset,data.timestamp);
    for(int i=0;i<8;i++)
    {
       MakeUnsignedChar(buffer_,offset,data.veh_id[i]);
    }
    return buffer_;
    
}

std::string HeartBeatRes::GetVehId()
{
    veh_id_.assign((char*)data_.veh_id,8);
    return veh_id_;
}

DWORD HeartBeatRes::GetMsgSeq()
{
    return data_.msg_seq;
}

TIMESTAMP HeartBeatRes::GetTimeStamp()
{
    return data_.timestamp;
}