#include "veh_data_req.h"
#include <string.h>
using namespace cvis_bcl;

VehDataReq::VehDataReq()
{
    msg_type="V2C_DATA_REQ";
}

VehDataReq::~VehDataReq()
{

}


BYTE* VehDataReq::MakeProtoPackage(VehDataReqData data,DWORD& length)
{
    uint32_t offset =0;
    HeaderData header = MakeHeader(msg_type,data.GetLength());
    length = data.GetLength()+sizeof(header);
    
    buffer_ = (BYTE*)malloc(length);
    memset(buffer_,0,length);
    
    header_.MakeHeader(buffer_,offset,header);
    MakeUnsignedInt(buffer_,offset,data.msg_seq);
    for(int i=0;i<8;i++)
    {
        MakeUnsignedChar(buffer_,offset,data.veh_id[i]);
    }
    MakeUnsignedChar(buffer_,offset,data.data_req_type);
    MakeUnsignedChar(buffer_,offset,data.data_req_param_len);
    for(int i=0;i<data.data_req_param_len;i++)
    {
        MakeUnsignedChar(buffer_,offset,*(data.data_req_param.data()+i));
    }
    return buffer_;
}