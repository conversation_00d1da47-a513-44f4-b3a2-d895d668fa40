/*---------------------------------------------------------------------
/* Copyright (c) 2023 tsari. All rights reserved. */
/**
* @Author: hlf
* @brief: 车端感知共享信息上报协议类
 *       
 * @version: 版本编号  修订日期  修订者 修订内容 （最新的在最前面）
 * --------------------------------------------------------------------
*/

#pragma once
#include "protocal_base.h"
namespace cvis_bcl
{
    class Detection:public ProtocalBase
    {
        public:
        Detection();
        virtual ~Detection();
        virtual bool ParseProtocal(BYTE* buffer,int length){};
        BYTE* MakeDetectionPackage(DetectionData data,DWORD& length);

        void MakePtcInfo(BYTE* buffer,DWORD& offset,PtcInfo data);

    };
}