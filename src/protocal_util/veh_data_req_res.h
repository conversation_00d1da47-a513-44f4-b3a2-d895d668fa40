/*---------------------------------------------------------------------
/* Copyright (c) 2023 tsari. All rights reserved. */
/**
* @Author: hlf
* @brief: 车辆静态数据请求返回协议类
 *       
 * @version: 版本编号  修订日期  修订者 修订内容 （最新的在最前面）
 * --------------------------------------------------------------------
*/
#pragma once
#include "protocal_base.h"
namespace cvis_bcl
{
    class VehDataReqRes:public ProtocalBase
    {
        public:
        VehDataReqRes();
        virtual ~VehDataReqRes();
        virtual bool ParseProtocal(BYTE* buffer,int length);
        VehDataReqResData GetData();
        private:
        VehDataReqResData data_;
    };
}