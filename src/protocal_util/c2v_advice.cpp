#include "c2v_advice.h"

using namespace cvis_bcl;

C2VAdvice::C2VAdvice()
{
    msg_type = "C2V_ADVICE";
}

C2VAdvice::~C2VAdvice()
{
}

bool C2VAdvice::ParseProtocal(BYTE* buffer,int length)
{
    DWORD offset=0;
    if(!header_.ParseHeader(buffer,offset,length))
    {
        return false;
    }
    if(header_.GetLength()+offset>length)
    {
        return false;
    }

    for(int i=0;i<8;i++)
    {
        data_.veh_id[i] = GetHostUnsignedChar(buffer,offset);
    }

    for(int i=0;i<8;i++)
    {
        data_.seq[i] = GetHostUnsignedInt(buffer,offset);
    }

    data_.advice_type = GetHostUnsignedChar(buffer,offset);
    data_.event_type = GetHostUnsignedChar(buffer,offset);
    data_.speed_exp = GetHostUnsignedShort(buffer,offset);
    data_.acc_exp = GetHostUnsignedShort(buffer,offset);
    data_.instrcut_direction = GetHostUnsignedChar(buffer,offset);
    data_.data_len = GetHostUnsignedShort(buffer,offset);
    for(int i=0;i<data_.data_len;i++)
    {
        data_.advice_data.push_back(GetHostUnsignedChar(buffer,offset));
    }
    return true;
}

C2VAdviceData C2VAdvice::GetData()
{
    return data_;
}