#include "subs_res.h"
#include <string.h>
using namespace cvis_bcl;

SubsRes::SubsRes()
{
    msg_type = "V2C_SUBS_RES";
}

SubsRes::~SubsRes()
{

}

BYTE* SubsRes::MakeSubsResPackage(SubsResData data, DWORD& length)
{
    // 使用 SubsResData 的 GetLength 方法计算实际数据长度
    HeaderData header = MakeHeader(msg_type, data.GetLength());
    length = data.GetLength() + sizeof(header);
    buffer_ = (BYTE*)malloc(length);
    memset(buffer_, 0, length);
    uint32_t offset = 0;
    header_.MakeHeader(buffer_, offset, header);
    MakePureDataPackage(buffer_, offset, data);
    return buffer_;
}

void SubsRes::MakePureDataPackage(BYTE* buffer, DWORD& offset, SubsResData data)
{
    // 车辆编号 vehicle_id[8]
    for(int i = 0; i < 8; i++)
    {
        MakeUnsignedChar(buffer, offset, data.vehicle_id[i]);
    }
    
    // 功能订阅回复长度 res_len
    MakeUnsignedChar(buffer, offset, data.res_len);
    
    // 功能订阅回复状态 res_info
    for(const auto& byte_data : data.res_info)
    {
        MakeUnsignedChar(buffer, offset, byte_data);
    }
}
