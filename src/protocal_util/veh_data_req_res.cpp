#include "veh_data_req_res.h"

using namespace cvis_bcl;

VehDataReqRes::VehDataReqRes()
{
    msg_type ="C2V_DATA_REQ_RES";
}

VehDataReqRes::~VehDataReqRes()
{

}

bool VehDataReqRes::ParseProtocal(BYTE* buffer,int length)
{
    DWORD offset=0;
    if(!header_.ParseHeader(buffer,offset,length))
    {
        return false;
    }
    if(header_.GetLength()+offset>length)
    {
        return false;
    }
    data_.msg_seq = GetHostUnsignedInt(buffer,offset);
    for(int i=0;i<8;i++)
    {
        data_.veh_id[i] = GetHostUnsignedChar(buffer,offset);
    }
    for(int i=0;i<36;i++)
    {
        data_.uuid[i] = GetHostUnsignedChar(buffer,offset);
    }
    data_.data_req_type = GetHostUnsignedChar(buffer,offset);
    data_.res_flag = GetHostUnsignedChar(buffer,offset);
    data_.data_res_param_len = GetHostUnsignedShort(buffer,offset);
    std::cout<<"offset "<<offset<<std::endl;
    data_.data_res_param.assign((char*)(buffer+offset),data_.data_res_param_len);
    return true;
}

VehDataReqResData VehDataReqRes::GetData()
{
    return data_;
}