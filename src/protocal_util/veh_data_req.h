/*---------------------------------------------------------------------
/* Copyright (c) 2023 tsari. All rights reserved. */
/**
* @Author: hlf
* @brief: 车辆静态数据请求协议类
 *       
 * @version: 版本编号  修订日期  修订者 修订内容 （最新的在最前面）
 * --------------------------------------------------------------------
*/
#pragma once
#include "protocal_base.h"
namespace cvis_bcl
{
    class VehDataReq:public ProtocalBase
    {
        public:
        VehDataReq();
        virtual ~VehDataReq();
        BYTE* MakeProtoPackage(VehDataReqData data,DWORD& length);
    };
}