#include "inh.h"
#include <string.h>
using namespace cvis_bcl;

INH::INH()
{
    msg_type = "V2C_INH";
}

INH::~INH()
{

}


BYTE* INH::MakeINHPackage(INHData data,DWORD& length)
{
    HeaderData header = MakeHeader(msg_type,data.GetLength());
    length = data.GetLength()+sizeof(header);
    buffer_ = (BYTE*)malloc(length);
    memset(buffer_,0,length);
    uint32_t offset =0;
    header_.MakeHeader(buffer_,offset,header);
    //MakeUnsignedInt(buffer_,offset,data.msg_seq);
    for(int i=0;i<8;i++)
    {
       MakeUnsignedChar(buffer_,offset,data.veh_id[i]);
    }
    MakeUnsignedChar(buffer_,offset,data.sw_version_length);
    for(int i=0;i<data.sw_version_length;i++)
    {
        MakeUnsignedChar(buffer_,offset,*(data.sw_version.data()+i));
    }
    MakeUnsignedChar(buffer_,offset,data.hw_version_length);
    for(int i=0;i<data.sw_version_length;i++)
    {
        MakeUnsignedChar(buffer_,offset,*(data.hw_version.data()+i));
    }
    MakeUnsignedChar(buffer_,offset,data.ad_version_length);
    for(int i=0;i<data.sw_version_length;i++)
    {
        MakeUnsignedChar(buffer_,offset,*(data.ad_version.data()+i));
    }
    MakeUnsignedChar(buffer_,offset,data.com_type);
    //MakeUnsignedChar(buffer_,offset,data.pc5_enable_type);
    MakeUnsignedChar(buffer_,offset,data.pos_confidence);
    MakeUnsignedChar(buffer_,offset,data.time_sync_type);
    MakeUnsignedChar(buffer_,offset,data.coordinate_type);
    MakeUnsignedChar(buffer_,offset,data.user_data_length);
    for(int i=0;i<data.user_data_length;i++)
    {
        MakeUnsignedChar(buffer_,offset,*(data.user_data.data()+i));
    }
    return buffer_;
}