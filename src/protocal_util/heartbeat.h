/*---------------------------------------------------------------------
/* Copyright (c) 2023 tsari. All rights reserved. */
/**
* @Author: hlf
* @brief: 心跳协议类
 *        1 定义心跳协议结构体 2 解析心跳协议 3 将结构体转换到心跳协议报文
 * @version: 版本编号  修订日期  修订者 修订内容 （最新的在最前面）
 * --------------------------------------------------------------------
*/
#pragma once
#include "protocal_base.h"
namespace cvis_bcl
{
    
    class HeartBeat:public ProtocalBase
    {
    public:
        HeartBeat();
        virtual ~HeartBeat();
        //解析心跳报文
        virtual bool ParseProtocal(BYTE* buffer,int length);
        //组装心跳报文,发送完成后需要调用Free()函数，释放申请的内存
        BYTE* MakeHeartBeatPackage(HeartBeatData data,DWORD& length);
        DWORD GetMsgSeq();
        TIMESTAMP GetTimeStamp();
        std::string GetVehId();
        
    private:
        HeartBeatData data_;
        std::string veh_id_;
    };
    

}