#include "rcu_status.h"
#include <string.h>

using namespace cvis_bcl;

RCUStatus::RCUStatus()
{
    msg_type = "RCU2CLOUD_STATUS";
}

RCUStatus::~RCUStatus()
{
}

BYTE* RCUStatus::MakeRCUStatusPackage(RCUStatusData data, DWORD& length)
{
    HeaderData header = MakeHeader(msg_type, data.getLength());
    length = data.getLength() + sizeof(HeaderData);
    buffer_ = (BYTE*)malloc(length);
    memset(buffer_, 0, length);

    uint32_t offset = 0;
    header_.MakeHeader(buffer_, offset, header);
   // MakeUnsignedInt(buffer_, offset, data.msg_seq);
    MakeUnsignedChar(buffer_, offset, data.channel_id);
    for (int i = 0; i < 8; i++)
    {
        MakeUnsignedChar(buffer_, offset, data.rcu_id[i]);
    }

    MakeUnsignedShort(buffer_, offset, data.status);
    MakeUnsignedChar(buffer_, offset, data.cam_num);
    for (auto camera : data.cam_status_list)
    {
        MakeUnsignedChar(buffer_, offset, camera.id);
        for (int j = 0; j < 11; j++)
        {
            MakeUnsignedChar(buffer_, offset, camera.cam_id[j]);
        }
        MakeUnsignedChar(buffer_, offset, camera.cam_status);
    }

    MakeUnsignedChar(buffer_, offset, data.radar_num);
    for (auto radar : data.radar_status_list)
    {
        MakeUnsignedChar(buffer_, offset, radar.id);
        for (int j = 0; j < 11; j++)
        {
            MakeUnsignedChar(buffer_, offset, radar.radar_id[j]);
        }
        MakeUnsignedChar(buffer_, offset, radar.radar_status);
    }

    MakeUnsignedChar(buffer_, offset, data.lidar_num);
    for (auto lidar : data.lidar_status_list)
    {
        MakeUnsignedChar(buffer_, offset, lidar.id);
        for (int j = 0; j < 11; j++)
        {
            MakeUnsignedChar(buffer_, offset, lidar.lidar_id[j]);
        }
        MakeUnsignedChar(buffer_, offset, lidar.lidar_status);
    }

    return buffer_;
}