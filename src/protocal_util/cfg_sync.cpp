#include "cfg_sync.h"

using namespace cvis_bcl;


CfgSync::CfgSync()
{
    msg_type= "C2V_CFG_SYNC";
}

CfgSync::~CfgSync()
{

}

bool CfgSync::ParseProtocal(BYTE* buffer,int length)
{
    DWORD offset=0;
    if(!header_.ParseHeader(buffer,offset,length))
    {
        return false;
    }
    if(header_.GetLength()+offset>length)
    {
        return false;
    }
    data_.msg_seq = GetHostUnsignedInt(buffer,offset);
    for(int i=0;i<8;i++)
    {
        data_.veh_id[i] = GetHostUnsignedChar(buffer,offset);
    }
    for(int i=0;i<36;i++)
    {
        data_.uuid[i] = GetHostUnsignedChar(buffer,offset);
    }
    data_.heartbeat_interval = GetHostUnsignedInt(buffer,offset);
    data_.veh_state_level = GetHostUnsignedChar(buffer,offset);
    data_.veh_state_interval = GetHostUnsignedInt(buffer,offset);
    data_.veh_status_interval = GetHostUnsignedInt(buffer,offset);
    data_.veh_event_update_switch = GetHostUnsignedChar(buffer,offset);
    data_.veh_detection_upload_switch=GetHostUnsignedChar(buffer,offset);
    data_.log_level = GetHostUnsignedChar(buffer,offset);
    return true;
}

CfgSyncData CfgSync::GetData()
{
    return data_;
}

