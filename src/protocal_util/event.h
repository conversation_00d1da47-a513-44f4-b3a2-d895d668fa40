/*---------------------------------------------------------------------
/* Copyright (c) 2023 tsari. All rights reserved. */
/**
* @Author: hlf
* @brief: 车辆事件信息上报协议类
 *       
 * @version: 版本编号  修订日期  修订者 修订内容 （最新的在最前面）
 * --------------------------------------------------------------------
*/

#pragma once
#include "protocal_base.h"
namespace cvis_bcl
{
    class Event:public ProtocalBase
    {
        public:
        Event();
        virtual ~Event();
        virtual bool ParseProtocal(BYTE* buffer,int length){};
        BYTE* MakeEventPackage(EventData data,DWORD& length);

        void MakeTrafficInfo(BYTE* buffer,DWORD& offset,TrafficEventInfo data);

    };
}