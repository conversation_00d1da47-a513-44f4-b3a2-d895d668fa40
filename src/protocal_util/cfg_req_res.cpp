#include "cfg_req_res.h"

using namespace cvis_bcl;

CfgReqRes::CfgReqRes()
{
    msg_type = "C2V_CFG_REQ_RES";
}

CfgReqRes::~CfgReqRes()
{

}

bool CfgReqRes::ParseProtocal(BYTE* buffer,int length)
{
    DWORD offset=0;
    if(!header_.ParseHeader(buffer,offset,length))
    {
        return false;
    }
    if(header_.GetLength()+offset>length)
    {
        return false;
    }
    data_.msg_seq = GetHostUnsignedInt(buffer,offset);
    for(int i=0;i<8;i++)
    {
        data_.veh_id[i] = GetHostUnsignedChar(buffer,offset);
    }
    for(int i=0;i<36;i++)
    {
        data_.uuid[i] = GetHostUnsignedChar(buffer,offset);
    }
    data_.heartbeat_interval = GetHostUnsignedInt(buffer,offset);
    data_.veh_state_level = GetHostUnsignedChar(buffer,offset);
    data_.veh_state_interval = GetHostUnsignedInt(buffer,offset);
    data_.veh_status_interval = GetHostUnsignedInt(buffer,offset);
    data_.veh_event_update_switch = GetHostUnsignedChar(buffer,offset);
    data_.veh_detection_upload_switch=GetHostUnsignedChar(buffer,offset);
    data_.log_level =GetHostUnsignedChar(buffer,offset);
    return true;
}

CfgReqResData CfgReqRes::GetData()
{
    return data_;
}