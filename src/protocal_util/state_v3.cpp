#include "state_v3.h"
#include "state_v2.h"
#include <string.h>
using namespace cvis_bcl;

StateV3::StateV3()
{
    msg_type = "V2C_STATE_V3";
}

StateV3::~StateV3()
{

}

BYTE* StateV3::MakeStateV3Package(StateV3Data data, DWORD& length)
{
    // 使用 StateV3Data 的 GetLength 方法计算实际数据长度
    HeaderData header = MakeHeader(msg_type, data.GetLength());
    length = data.GetLength() + sizeof(header);
    buffer_ = (BYTE*)malloc(length);
    memset(buffer_, 0, length);
    uint32_t offset = 0;
    header_.MakeHeader(buffer_, offset, header);
    MakePureDataPackage(buffer_, offset, data);
    return buffer_;
}

void StateV3::MakePureDataPackage(BYTE* buffer, DWORD& offset, StateV3Data data)
{
    // 首先序列化 StateV2Data 部分
    StateV2 state_v2;
    state_v2.MakePureDataPackage(buffer, offset, data.v2_data);
    
    // 然后序列化 StateV3Data 特有的字段
    
    // 执行云端控制命令编号
    MakeUnsignedInt(buffer, offset, data.cloud_message_id);
    
    // 自动驾驶需求加速度
    MakeUnsignedShort(buffer, offset, data.accel_cmd);
    
    // 自动驾驶需求扭矩
    MakeUnsignedInt(buffer, offset, data.torque_cmd);
    
    // 自动驾驶需求速度
    MakeUnsignedShort(buffer, offset, data.velocity_cmd);
    
    // 规划-位置点轨迹数量
    MakeUnsignedShort(buffer, offset, data.planning_loc_num);
    
    // 规划-位置点轨迹列表
    for(const auto& trace_point : data.planning_loc_tracepoints)
    {
        MakeUnsignedInt(buffer, offset, trace_point.exp_longitude);
        MakeUnsignedInt(buffer, offset, trace_point.exp_latitude);
        MakeUnsignedShort(buffer, offset, trace_point.exp_speed);
        MakeUnsignedShort(buffer, offset, trace_point.exp_acceleration);
        MakeUnsignedInt(buffer, offset, trace_point.exp_heading);
    }
    
    // 决策-换道
    MakeUnsignedChar(buffer, offset, data.decision_lane_change);
    
    // 加减速
    MakeUnsignedChar(buffer, offset, data.decision_accel);
    
    // 决策-转向
    MakeUnsignedChar(buffer, offset, data.decision_turn_signal);
    
    // 决策-车辆状态
    MakeUnsignedChar(buffer, offset, data.decision_vehicle_status);
    
    // 感知目标个数
    MakeUnsignedShort(buffer, offset, data.detection_len);
    
    // 感知目标数据列表
    for(const auto& detection : data.detection_data)
    {
        // DetectionData 结构体的序列化比较复杂，这里简化处理
        // 实际应用中需要完整实现 DetectionData 的所有字段序列化
        
        // uuid[36]
        for(int i = 0; i < 36; i++)
        {
            MakeUnsignedChar(buffer, offset, detection.uuid[i]);
        }
        
        // obj_id
        MakeUnsignedShort(buffer, offset, detection.obj_id);
        
        // type
        MakeUnsignedChar(buffer, offset, detection.type);
        
        // status
        MakeUnsignedChar(buffer, offset, detection.status);
        
        // len, width, height
        MakeUnsignedShort(buffer, offset, detection.len);
        MakeUnsignedShort(buffer, offset, detection.width);
        MakeUnsignedShort(buffer, offset, detection.height);
        
        // longitude, latitude
        MakeUnsignedInt(buffer, offset, detection.longitude);
        MakeUnsignedInt(buffer, offset, detection.latitude);
        
        // loc_east, loc_north
        MakeUnsignedInt(buffer, offset, detection.loc_east);
        MakeUnsignedInt(buffer, offset, detection.loc_north);
        
        // pos_confidence
        MakeUnsignedChar(buffer, offset, detection.pos_confidence);
        
        // elevation
        MakeUnsignedInt(buffer, offset, detection.elevation);
        
        // elev_confidence
        MakeUnsignedChar(buffer, offset, detection.elev_confidence);
        
        // speed
        MakeUnsignedShort(buffer, offset, detection.speed);
        
        // speed_confidence
        MakeUnsignedChar(buffer, offset, detection.speed_confidence);
        
        // speed_east
        MakeUnsignedShort(buffer, offset, detection.speed_east);
        
        // speed_east_confidence
        MakeUnsignedChar(buffer, offset, detection.speed_east_confidence);
        
        // speed_north
        MakeUnsignedShort(buffer, offset, detection.speed_north);
        
        // speed_north_confidence
        MakeUnsignedChar(buffer, offset, detection.speed_north_confidence);
        
        // heading
        MakeUnsignedInt(buffer, offset, detection.heading);
        
        // heading_confidence
        MakeUnsignedChar(buffer, offset, detection.heading_confidence);
        
        // accel_vert
        MakeUnsignedShort(buffer, offset, detection.accel_vert);
        
        // accel_vert_confidence
        MakeUnsignedChar(buffer, offset, detection.accel_vert_confidence);
        
        // tracking_times
        MakeUnsignedInt(buffer, offset, detection.tracking_times);
        
        // hist_loc_num
        MakeUnsignedShort(buffer, offset, detection.hist_loc_num);
        
        // 历史轨迹点列表
        for(const auto& hist_loc : detection.hist_locs)
        {
            MakeUnsignedInt(buffer, offset, hist_loc.position.longitude);
            MakeUnsignedInt(buffer, offset, hist_loc.position.latitude);
            MakeUnsignedChar(buffer, offset, hist_loc.pos_confidence);
            MakeUnsignedShort(buffer, offset, hist_loc.speed);
            MakeUnsignedChar(buffer, offset, hist_loc.speed_confidence);
            MakeUnsignedInt(buffer, offset, hist_loc.heading);
            MakeUnsignedChar(buffer, offset, hist_loc.heading_confidence);
        }
        
        // pred_loc_num
        MakeUnsignedShort(buffer, offset, detection.pred_loc_num);
        
        // 预测轨迹点列表
        for(const auto& pred_loc : detection.pred_locs)
        {
            MakeUnsignedInt(buffer, offset, pred_loc.position.longitude);
            MakeUnsignedInt(buffer, offset, pred_loc.position.latitude);
            MakeUnsignedChar(buffer, offset, pred_loc.pos_confidence);
            MakeUnsignedShort(buffer, offset, pred_loc.speed);
            MakeUnsignedChar(buffer, offset, pred_loc.speed_confidence);
            MakeUnsignedInt(buffer, offset, pred_loc.heading);
            MakeUnsignedChar(buffer, offset, pred_loc.heading_confidence);
        }
        
        // lane_id
        MakeUnsignedChar(buffer, offset, detection.lane_id);
        
        // filter_info_type
        MakeUnsignedChar(buffer, offset, detection.filter_info_type);
        
        // plate_no_len
        MakeUnsignedChar(buffer, offset, detection.plate_no_len);
        
        // plate_no
        for(int i = 0; i < detection.plate_no_len; i++)
        {
            MakeUnsignedChar(buffer, offset, *(detection.plate_no.data() + i));
        }
        
        // plate_type
        MakeUnsignedChar(buffer, offset, detection.plate_type);
        
        // plate_color
        MakeUnsignedChar(buffer, offset, detection.plate_color);
        
        // vehicle_color
        MakeUnsignedChar(buffer, offset, detection.vehicle_color);
    }
    
    // 自动驾驶系统故障
    MakeUnsignedShort(buffer, offset, data.auto_driving_sys_fault);
    
    // 电子手刹状态
    MakeUnsignedChar(buffer, offset, data.epb_flag);
    
    // 自定义字段长度
    MakeUnsignedChar(buffer, offset, data.userdefined_data_length);
    
    // 自定义字段内容
    for(int i = 0; i < data.userdefined_data_length; i++)
    {
        MakeUnsignedChar(buffer, offset, *(data.userdefined_data.data() + i));
    }
}
