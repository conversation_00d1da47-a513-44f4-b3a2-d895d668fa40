#include "cfg_req.h"
#include <string.h>

using namespace cvis_bcl;

CfgReq::CfgReq()
{
    msg_type = "V2C_CFG_REQ";
}

CfgReq::~CfgReq()
{

}

BYTE* CfgReq::MakeCfgReqPackage(CfgReqData data,DWORD& length)
{
    HeaderData header = MakeHeader(msg_type,sizeof(data));
    length = sizeof(data)+sizeof(header);
    
    buffer_ = (BYTE*)malloc(length);
    memset(buffer_,0,length);
    uint32_t offset =0;
    header_.MakeHeader(buffer_,offset,header);
    MakeUnsignedInt(buffer_,offset,data.msg_seq);
    for(int i=0;i<8;i++)
    {
       MakeUnsignedChar(buffer_,offset,data.veh_id[i]);
    }
    return buffer_;
}