#include "interaction_req.h"
#include "string.h"
using namespace cvis_bcl;

InteractionReq::InteractionReq()
{
    msg_type = "V2C_INTERACTION_REQ";
}

InteractionReq::~InteractionReq()
{

}


BYTE* InteractionReq::MakeProtocalPackage(InteractionReqData data,DWORD& length)
{
    HeaderData header = MakeHeader(msg_type,data.GetLength());
    length = data.GetLength()+sizeof(header);
    buffer_ = (BYTE*)malloc(length);
    memset(buffer_,0,length);
    uint32_t offset =0;
    header_.MakeHeader(buffer_,offset,header);
    MakeUnsignedInt(buffer_,offset,data.msg_seq);
    for(int i=0;i<8;i++)
    {
       MakeUnsignedChar(buffer_,offset,data.veh_id[i]);
    }
    MakeUnsignedChar(buffer_,offset,data.control_mode);
    return buffer_;
}


