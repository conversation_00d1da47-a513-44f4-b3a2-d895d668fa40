#include "subs.h"
#include <string.h>
using namespace cvis_bcl;

Subs::Subs()
{
    msg_type = "V2C_SUBS";
}

Subs::~Subs()
{

}

BYTE* Subs::MakeSubsPackage(SubsData data, DWORD& length)
{
    // 使用 SubsData 的 GetLength 方法计算实际数据长度
    HeaderData header = MakeHeader(msg_type, data.GetLength());
    length = data.GetLength() + sizeof(header);
    buffer_ = (BYTE*)malloc(length);
    memset(buffer_, 0, length);
    uint32_t offset = 0;
    header_.MakeHeader(buffer_, offset, header);
    MakePureDataPackage(buffer_, offset, data);
    return buffer_;
}

void Subs::MakePureDataPackage(BYTE* buffer, DWORD& offset, SubsData data)
{
    // 车辆编号 vehicle_id[8]
    for(int i = 0; i < 8; i++)
    {
        MakeUnsignedChar(buffer, offset, data.vehicle_id[i]);
    }
    
    // 功能订阅信息长度 subs_len
    MakeUnsignedChar(buffer, offset, data.subs_len);
    
    // 功能订阅信息 subs_info
    for(const auto& byte_data : data.subs_info)
    {
        MakeUnsignedChar(buffer, offset, byte_data);
    }
}
