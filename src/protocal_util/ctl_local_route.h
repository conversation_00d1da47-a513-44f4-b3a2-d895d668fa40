/*---------------------------------------------------------------------
/* Copyright (c) 2023 tsari. All rights reserved. */
/**
* @Author: hlf
* @brief: 云端局部路径控制指令协议类
 *       
 * @version: 版本编号  修订日期  修订者 修订内容 （最新的在最前面）
 * --------------------------------------------------------------------
*/
#pragma once
#include "protocal_base.h"
namespace cvis_bcl
{
    class CtlLocalRoute:public ProtocalBase
    {
        public:
        CtlLocalRoute();
        virtual ~CtlLocalRoute();
        virtual bool ParseProtocal(BYTE* buffer,int length);
        bool ParseTrajectoryPoint(BYTE* buffer,DWORD& offset,TrajectoryPoint& point );
        CtlLocalRouteData GetData();
        private:
        CtlLocalRouteData data_;
    };

}