/*---------------------------------------------------------------------
/* Copyright (c) 2023 tsari. All rights reserved. */
/**
* @Author: hlf
* @brief: 车辆运行状态信息补发响应协议类
 *        1. 实现StateResendResData结构体的序列化
 *        2. 支持车辆运行状态信息补发响应的数据打包
 * @version: 版本编号  修订日期  修订者 修订内容 （最新的在最前面）
 * --------------------------------------------------------------------
*/

#pragma once
#include "protocal_base.h"
namespace cvis_bcl
{
    
    class StateResendRes : public ProtocalBase
    {
    public:
        StateResendRes();
        virtual ~StateResendRes();
        
        virtual bool ParseProtocal(BYTE* buffer, int length) override { return false; };
        
        //组装state报文,发送完成后需要调用Free()函数，释放申请的内存
        BYTE* MakeStateResendResPackage(StateResendResData data, DWORD& length);
        
        void MakePureDataPackage(BYTE* buffer, DWORD& offset, StateResendResData data);
    };

}
