#include "detection.h"
#include "string.h"

using namespace cvis_bcl;

Detection::Detection()
{
    msg_type="V2C_DETECTION";
}

Detection::~Detection()
{

}


BYTE* Detection::MakeDetectionPackage(DetectionData data,DWORD& length)
{
    HeaderData header = MakeHeader(msg_type,data.GetLength());
    length = data.GetLength()+sizeof(header);
    
    buffer_ = (BYTE*)malloc(length);
    memset(buffer_,0,length);
    uint32_t offset =0;
    header_.MakeHeader(buffer_,offset,header);
    MakeUnsignedInt(buffer_,offset,data.msg_seq);
    for(int i=0;i<8;i++)
    {
        MakeUnsignedChar(buffer_,offset,data.veh_id[i]);
    }
    MakeUsignedLongLong(buffer_,offset,data.timestamp);
    MakeUnsignedChar(buffer_,offset,data.ptc_num);
    for(int i=0;i<data.ptc_num;i++)
    {
        MakePtcInfo(buffer_,offset,data.ptc_infos[i]);
    }
    return buffer_;

}


void Detection::MakePtcInfo(BYTE* buffer,DWORD& offset,PtcInfo data)
{
    MakeUnsignedChar(buffer,offset,data.ptc_id);
    MakeUnsignedChar(buffer,offset,data.motion_status);
    MakeUnsignedChar(buffer,offset,data.ptc_type);
    MakeUnsignedInt(buffer,offset,MakeLongitude(data.position.longitude));
    MakeUnsignedInt(buffer,offset,MakeLatitude(data.position.latitude));
    MakeUnsignedInt(buffer,offset,MakeElevation(data.position.elevation));
    MakeUnsignedChar(buffer,offset,data.pos_confidence);
    MakeUnsignedChar(buffer,offset,data.ele_confidence);
    MakeUnsignedShort(buffer,offset,MakeWORD(data.speed,0.01,0));
    MakeUnsignedInt(buffer,offset,MakeDWORD(data.heading,1e-4,0));
    MakeUnsignedInt(buffer,offset,data.size.length);
    MakeUnsignedInt(buffer,offset,data.size.width);
    MakeUnsignedInt(buffer,offset,data.size.height);
    MakeUnsignedChar(buffer,offset,data.color);
    for(int i=0;i<9;i++)
    {
        MakeUnsignedChar(buffer,offset,data.plate_No[i]);
    }
    MakeUnsignedInt(buffer,offset,data.tracking_time);
    MakeUnsignedChar(buffer,offset,data.lane_id);
}