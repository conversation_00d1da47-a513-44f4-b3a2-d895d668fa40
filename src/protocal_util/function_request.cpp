#include "function_request.h"
#include <string.h>
using namespace cvis_bcl;

FunctionRequest::FunctionRequest()
{
    msg_type ="V2C_FUNC_REQ";
}

FunctionRequest::~FunctionRequest()
{

}

bool FunctionRequest::ParseProtocal(BYTE* buffer,int length)
{
    DWORD offset=0;
    if(!header_.ParseHeader(buffer,offset,length))
    {
        return false;
    }
    if(header_.GetLength()+offset>length)
    {
        return false;
    }
    data_.msg_seq = GetHostUnsignedInt(buffer,offset);
    for(int i=0;i<8;i++)
    {
        data_.veh_id[i] = GetHostUnsignedChar(buffer,offset); 
    }
    for(int i=0;i<6;i++)
    {
        data_.func_req[i] = GetBit2Byte(buffer,offset);
    }
    data_.wired_control_mode_hori = GetHostUnsignedChar(buffer,offset);
    data_.wired_control_mode_vert = GetHostUnsignedChar(buffer,offset);
    data_.wired_control_mode_brake = GetHostUnsignedChar(buffer,offset);
    data_.ota_permission = GetHostUnsignedChar(buffer,offset);
    data_.camera_share_permission = GetHostUnsignedChar(buffer,offset);
    data_.detection_sahre_permission = GetHostUnsignedChar(buffer,offset);
    data_.localizaiton_level = GetHostUnsignedChar(buffer,offset);
    data_.veh_state_level = GetHostUnsignedChar(buffer,offset);
    data_.veh_state_interval = GetHostUnsignedChar(buffer,offset);
    data_.local_map_info_length = GetHostUnsignedChar(buffer,offset);
    for(int i=0;i<data_.local_map_info_length;i++)
    {
        data_.local_map_info.push_back(buffer[offset++]);
    }
    return true;
}

BYTE* FunctionRequest::MakeFunctionRequstPackage(FuncReqData data,DWORD& length)
{
    uint32_t offset =0;
    HeaderData header = MakeHeader(msg_type,data.GetLength());
    length = data.GetLength()+sizeof(header);
    buffer_ = (BYTE*)malloc(length);
    memset(buffer_,0,length);
    header_.MakeHeader(buffer_,offset,header);
    MakeUnsignedInt(buffer_,offset,data.msg_seq);
    for(int i=0;i<8;i++)
    {
        MakeUnsignedChar(buffer_,offset,data.veh_id[i]);
    }
    for(int i=0;i<6;i++)
    {
        MakeBitByte(buffer_,offset,data.func_req[i]);
    }
    MakeUnsignedChar(buffer_,offset,data.wired_control_mode_hori);
    MakeUnsignedChar(buffer_,offset,data.wired_control_mode_vert);
    MakeUnsignedChar(buffer_,offset,data.wired_control_mode_brake);
    MakeUnsignedChar(buffer_,offset,data.ota_permission);
    MakeUnsignedChar(buffer_,offset,data.camera_share_permission);
    MakeUnsignedChar(buffer_,offset,data.detection_sahre_permission);
    MakeUnsignedChar(buffer_,offset,data.localizaiton_level);
    MakeUnsignedChar(buffer_,offset,data.veh_state_level);
    MakeUnsignedChar(buffer_,offset,data.veh_state_interval);
    MakeUnsignedChar(buffer_,offset,data.local_map_info_length);
    for(int i=0;i<data.local_map_info_length;i++)
    {
        MakeUnsignedChar(buffer_,offset,*(data.local_map_info.data()+i));
    }
    return buffer_;
}

FuncReqData FunctionRequest::GetData()
{
    return data_;
}


void FunctionRequest::MakeFuncReqBytes(std::vector<std::string> requests,FuncReqData& data)
{
    
}