/*---------------------------------------------------------------------
/* Copyright (c) 2023 tsari. All rights reserved. */
/**
* @Author: hlf
* @brief: 服务订阅协议类
 *        
 * @version: 版本编号  修订日期  修订者 修订内容 （最新的在最前面）
 * --------------------------------------------------------------------
*/
#pragma once
#include "protocal_base.h"

namespace cvis_bcl
{
    class FunctionRequest:public ProtocalBase
    {
    public:
        FunctionRequest();
        virtual ~FunctionRequest();
        virtual bool ParseProtocal(BYTE* buffer,int length);
        //由请求的服务类型字符串组装byte
        void MakeFuncReqBytes(std::vector<std::string> requests,FuncReqData& data);
        //组装服务订阅报文,发送完成后需要调用Free()函数，释放申请的内存
        BYTE* MakeFunctionRequstPackage(FuncReqData data,DWORD& length);
        FuncReqData GetData();
        
    private:
        FuncReqData data_;
    };
}

