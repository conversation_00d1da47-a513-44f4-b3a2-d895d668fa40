#include "ctl_remote_driving.h"

using namespace cvis_bcl;

CtlRemoteDriving::CtlRemoteDriving()
{

}

CtlRemoteDriving::~CtlRemoteDriving()
{

}

bool CtlRemoteDriving::ParseProtocal(BYTE* buffer,int length)
{
    DWORD offset=0;
    if(!header_.ParseHeader(buffer,offset,length))
    {
        return false;
    }
    if(header_.GetLength()+offset>length)
    {
        return false;
    }
    data_.msg_seq = GetHostUnsignedInt(buffer,offset);
    for(int i=0;i<8;i++)
    {
        data_.veh_id[i] = GetHostUnsignedChar(buffer,offset);
    }
    data_.timestamp = GetHostlonglong(buffer,offset);
    data_.auto_drive_mode_switch = GetHostUnsignedChar(buffer,offset);
    data_.steering_angle = GetValue<float,DWORD>(GetHostUnsignedInt(buffer,offset),1e-4,-10000000);
    data_.steering_angular_velocity = GetValue<float,WORD>(GetHostUnsignedShort(buffer,offset),0.01,-10000);
    data_.accel_pos = GetValue<float,WORD>(GetHostUnsignedShort(buffer,offset),0.1,0);
    data_.brake_flag = GetHostUnsignedChar(buffer,offset);
    data_.brake_pos =GetValue<float,WORD>(GetHostUnsignedShort(buffer,offset),0.1,0);
    data_.tap_pos = GetHostUnsignedChar(buffer,offset);
    data_.lights = GetHostUnsignedShort(buffer,offset);
    data_.wipers = GetHostUnsignedChar(buffer,offset);
    data_.doors = GetHostUnsignedShort(buffer,offset);
    data_.windows = GetHostUnsignedShort(buffer,offset);
    return true;
}

CtlRemoteDrivingData CtlRemoteDriving::GetData()
{
    return data_;
}