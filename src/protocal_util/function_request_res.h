/*---------------------------------------------------------------------
/* Copyright (c) 2023 tsari. All rights reserved. */
/**
* @Author: hlf
* @brief: 服务订阅协议类
 *        
 * @version: 版本编号  修订日期  修订者 修订内容 （最新的在最前面）
 * --------------------------------------------------------------------
*/
#pragma once
#include "protocal_base.h"

namespace cvis_bcl
{
    class FunctionRequestRes:public ProtocalBase
    {
        public:
        FunctionRequestRes();
        virtual ~FunctionRequestRes();
         virtual bool ParseProtocal(BYTE* buffer,int length);
        
        FuncReqDataRes GetData();
        std::vector<std::string> GetFunReqList();
    private:
        FuncReqDataRes data_;
    };

}