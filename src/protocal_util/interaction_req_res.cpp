#include "interaction_req_res.h"

using namespace cvis_bcl;


InteractionReqRes::InteractionReqRes()
{
    msg_type = "C2V_INTERACTION_REQ_RES";
}

InteractionReqRes::~InteractionReqRes()
{

}


bool InteractionReqRes::ParseProtocal(BYTE* buffer,int length)
{
    DWORD offset=0;
    if(!header_.ParseHeader(buffer,offset,length))
    {
        return false;
    }
    if(header_.GetLength()+offset>length)
    {
        return false;
    }
    data_.msg_seq = GetHostUnsignedInt(buffer,offset);
    for(int i=0;i<8;i++)
    {
        data_.veh_id[i] = GetHostUnsignedChar(buffer,offset);
    }
    for(int i=0;i<36;i++)
    {
        data_.uuid[i] = GetHostUnsignedChar(buffer,offset);
    }
    data_.do_flag = GetHostUnsignedChar(buffer,offset);
    data_.error_code = GetHostUnsignedChar(buffer,offset);
    data_.content_len = GetHostUnsignedChar(buffer,offset);
    data_.content.assign((char*)(buffer+offset),data_.content_len);
    return true;
}

InteractionReqResData InteractionReqRes::GetData()
{
    return data_;
}