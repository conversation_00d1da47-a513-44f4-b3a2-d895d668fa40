/*---------------------------------------------------------------------
/* Copyright (c) 2023 tsari. All rights reserved. */
/**
* @Author: hlf
* @brief: 云端下发车辆配置信息指令协议类
 *       
 * @version: 版本编号  修订日期  修订者 修订内容 （最新的在最前面）
 * --------------------------------------------------------------------
*/

#pragma once
#include "protocal_base.h"
namespace cvis_bcl
{
    class CfgSyncRes:public ProtocalBase
    {
    public:
        CfgSyncRes();
        ~CfgSyncRes();
        virtual bool ParseProtocal(BYTE* buffer,int length){};
        //组装云端下发车辆配置信息报文,发送完成后需要调用Free()函数，释放申请的内存
        BYTE* MakeCfgSyncResPackage(CfgSyncResData data,DWORD& length);
    };
}