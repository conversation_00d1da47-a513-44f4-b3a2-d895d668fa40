#include "state_v2.h"
#include "state_v1.h"
#include <string.h>
using namespace cvis_bcl;

StateV2::StateV2()
{
    msg_type = "V2C_STATE_V2";
}

StateV2::~StateV2()
{

}

BYTE* StateV2::MakeStateV2Package(StateV2Data data, DWORD& length)
{
    // 使用 StateV2Data 的 GetLength 方法计算实际数据长度
    HeaderData header = MakeHeader(msg_type, data.GetLength());
    length = data.GetLength() + sizeof(header);
    buffer_ = (BYTE*)malloc(length);
    memset(buffer_, 0, length);
    uint32_t offset = 0;
    header_.MakeHeader(buffer_, offset, header);
    MakePureDataPackage(buffer_, offset, data);
    return buffer_;
}

void StateV2::MakePureDataPackage(BYTE* buffer, DWORD& offset, StateV2Data data)
{
    // 首先序列化 StateV1Data 部分
    StateV1 state_v1;
    state_v1.MakePureDataPackage(buffer, offset, data.v1_data);

    // 然后序列化 StateV2Data 特有的字段

    // ABS 状态
    MakeUnsignedChar(buffer, offset, data.abs_flag);

    // TCS 状态
    MakeUnsignedChar(buffer, offset, data.tcs_flag);

    // ESP 状态
    MakeUnsignedChar(buffer, offset, data.esp_flag);

    // LKA 状态
    MakeUnsignedChar(buffer, offset, data.lka_flag);

    // ACC 工作模式
    MakeUnsignedChar(buffer, offset, data.acc_mode);

    // FCW 状态
    MakeUnsignedChar(buffer, offset, data.fcw_flag);

    // LDW 状态
    MakeUnsignedChar(buffer, offset, data.ldw_flag);

    // AEB 状态
    MakeUnsignedChar(buffer, offset, data.aeb_flag);

    // LCA 状态
    MakeUnsignedChar(buffer, offset, data.lca_flag);

    // DMS 状态
    MakeUnsignedChar(buffer, offset, data.dms_flag);

    // 里程
    MakeUnsignedInt(buffer, offset, data.mileage);

    // 油量
    MakeUnsignedShort(buffer, offset, data.fuel_gauge);

    // 电池剩余电量
    MakeUnsignedShort(buffer, offset, data.soc);

    // 电池温度
    MakeUnsignedChar(buffer, offset, data.temperature);

    // 预计续航里程
    MakeUnsignedInt(buffer, offset, data.endurance);

    // 车辆故障状态
    MakeUnsignedShort(buffer, offset, data.veh_fault);

    // 电机转速
    MakeUnsignedShort(buffer, offset, data.motor_speed);

    // 电机转矩
    MakeUnsignedInt(buffer, offset, data.motor_torque);

    // 运行模式
    MakeUnsignedChar(buffer, offset, data.veh_mode);

    // 充电状态
    MakeUnsignedChar(buffer, offset, data.charge_state);

    // 动力电池实时电压
    MakeUnsignedShort(buffer, offset, data.batt_vol);

    // 动力电池实时电流
    MakeUnsignedShort(buffer, offset, data.batt_cur);

    // 喇叭状态
    MakeUnsignedChar(buffer, offset, data.horn_state);

    // 车轮数
    MakeUnsignedChar(buffer, offset, data.wheel_num);

    // 轮速数据
    for(const auto& velocity : data.wheel_velocity)
    {
        MakeUnsignedShort(buffer, offset, velocity);
    }

    // 胎压数据
    for(const auto& pressure : data.wheel_pressure)
    {
        MakeUnsignedShort(buffer, offset, pressure);
    }

    // 车灯状态
    MakeUnsignedShort(buffer, offset, data.lights);

    // 车门状态
    MakeUnsignedShort(buffer, offset, data.doors);

    // 自定义字段长度
    MakeUnsignedChar(buffer, offset, data.userdefined_data_length);

    // 自定义字段内容
    for(int i = 0; i < data.userdefined_data_length; i++)
    {
        MakeUnsignedChar(buffer, offset, *(data.userdefined_data.data() + i));
    }
}