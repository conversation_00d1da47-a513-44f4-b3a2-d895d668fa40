#include "c2v_advice_res.h"
#include <string.h>

using namespace cvis_bcl;

C2VAdviceRes::C2VAdviceRes()
{
    msg_type = "V2C_ADVICE_RES";
}

C2VAdviceRes::~C2VAdviceRes()
{
}

BYTE* C2VAdviceRes::MakeC2VAdviceResPackage(C2VAdviceResData data,DWORD& length)
{
    HeaderData header = MakeHeader(msg_type,sizeof(data));
    length = sizeof(data)+sizeof(header);
    
    buffer_ = (BYTE*)malloc(length);
    memset(buffer_,0,length);
    uint32_t offset =0;
    header_.MakeHeader(buffer_,offset,header);
    MakeUnsignedChar(buffer_,offset,data.do_flag);
    return buffer_;
}