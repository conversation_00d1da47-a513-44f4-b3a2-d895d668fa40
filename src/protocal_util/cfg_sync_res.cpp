#include "cfg_sync_res.h"
#include <string.h>

using namespace cvis_bcl;

CfgSyncRes::CfgSyncRes()
{
    msg_type= "V2C_CFG_SYNC_RES";
}

CfgSyncRes::~CfgSyncRes()
{

}

BYTE* CfgSyncRes::MakeCfgSyncResPackage(CfgSyncResData data,DWORD& length)
{
    HeaderData header = MakeHeader(msg_type,sizeof(data));
    length = sizeof(data)+sizeof(header);
    
    buffer_ = (BYTE*)malloc(length);
    memset(buffer_,0,length);
    uint32_t offset =0;
    header_.MakeHeader(buffer_,offset,header);
    MakeUnsignedInt(buffer_,offset,data.msg_seq);
    for(int i=0;i<8;i++)
    {
        MakeUnsignedChar(buffer_,offset,data.veh_id[i]);
    }
    MakeUnsignedChar(buffer_,offset,data.do_flag);
    return buffer_;
}
