#include "ctl_local_route.h"

using namespace cvis_bcl;

CtlLocalRoute::CtlLocalRoute()
{

}

CtlLocalRoute::~CtlLocalRoute()
{

}

bool CtlLocalRoute::ParseProtocal(BYTE* buffer,int length)
{
    DWORD offset=0;
    if(!header_.ParseHeader(buffer,offset,length))
    {
        return false;
    }
    if(header_.GetLength()+offset>length)
    {
        return false;
    }
    data_.msg_seq = GetHostUnsignedInt(buffer,offset);
    for(int i=0;i<8;i++)
    {
        data_.veh_id[i] = GetHostUnsignedChar(buffer,offset);
    }
    data_.timestamp = GetHostlonglong(buffer,offset);
    data_.auto_drive_mode_switch = GetHostUnsignedChar(buffer,offset);
    data_.ref_longitude = GetLongitude(GetHostUnsignedInt(buffer,offset));
    data_.ref_latitude = GetLatitude(GetHostUnsignedInt(buffer,offset));
    data_.ref_elevation =GetElevation(GetHostUnsignedInt(buffer,offset));
    data_.ref_heading = GetDouble(GetHostUnsignedInt(buffer,offset),1e-4,0);
    data_.local_routeway_point_num = GetHostUnsignedChar(buffer,offset);
    for(int i=0;i<data_.local_routeway_point_num;i++)
    {
        TrajectoryPoint point;
        ParseTrajectoryPoint(buffer,offset,point);
        data_.trajectory_points.push_back(point);
    }
    return true;
}

bool CtlLocalRoute::ParseTrajectoryPoint(BYTE* buffer,DWORD& offset,TrajectoryPoint& point )
{
    point.x = GetValue<float,WORD>(GetHostUnsignedShort(buffer,offset),0.01,-10000);
    point.y = GetValue<float,WORD>(GetHostUnsignedShort(buffer,offset),0.01,-30000);
    point.z = GetValue<float,WORD>(GetHostUnsignedShort(buffer,offset),0.01,-10000);
    point.theta = GetValue<float,DWORD>(GetHostUnsignedInt(buffer,offset),1e-4,1800000);
    point.kappa = GetValue<float,WORD>(GetHostUnsignedShort(buffer,offset),1e-4,0);
    point.s = GetValue<float,WORD>(GetHostUnsignedShort(buffer,offset),0.01,-10000);
    point.dkappa = GetValue<float,WORD>(GetHostUnsignedShort(buffer,offset),0.01,-10000);
    point.v = GetValue<float,WORD>(GetHostUnsignedShort(buffer,offset),0.01,0);
    point.a = GetValue<float,WORD>(GetHostUnsignedShort(buffer,offset),0.01,-10000);
    point.relative_time = GetValue<DWORD,DWORD>(GetHostUnsignedInt(buffer,offset),1,-3600000);
    return true;
}

CtlLocalRouteData CtlLocalRoute::GetData()
{
    return data_;
}