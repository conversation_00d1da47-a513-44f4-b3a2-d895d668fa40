#include "state_v1.h"
#include <string.h>
using namespace cvis_bcl;

StateV1::StateV1()
{
    msg_type = "V2C_STATE_V1";
}

StateV1::~StateV1()
{

}

BYTE* StateV1::MakeStateV1Package(StateV1Data data,DWORD& length)
{
    // 使用 StateV1Data 的 GetLength 方法计算实际数据长度
    HeaderData header = MakeHeader(msg_type, data.GetLength());
    length = data.GetLength() + sizeof(header);
    buffer_ = (BYTE*)malloc(length);
    memset(buffer_, 0, length);
    uint32_t offset = 0;
    header_.MakeHeader(buffer_, offset, header);
    MakePureDataPackage(buffer_, offset, data);
    return buffer_;
}

void StateV1::MakePureDataPackage(BYTE* buffer, DWORD& offset, StateV1Data data)
{
    // 车辆编号 veh_id[8]
    for(int i = 0; i < 8; i++)
    {
        MakeUnsignedChar(buffer_, offset, data.veh_id[i]);
    }

    // 消息编号 msg_id[8]
    for(int i = 0; i < 8; i++)
    {
        MakeUnsignedChar(buffer_, offset, data.msg_id[i]);
    }

    // GNSS时间戳 timestamp_gnss (TIMESTAMP = uint64_t)
    MakeUsignedLongLong(buffer_, offset, data.timestamp_gnss);

    // GNSS速度 velocity_gnss (DWORD)
    MakeUnsignedInt(buffer_, offset, data.velocity_gnss);

    // 位置信息 position (WayPointLLE: longitude, latitude, elevation)
    MakeUnsignedInt(buffer_, offset, data.position.longitude);
    MakeUnsignedInt(buffer_, offset, data.position.latitude);
    MakeUnsignedInt(buffer_, offset, data.position.elevation);

    // 航向角 heading (DWORD)
    MakeUnsignedInt(buffer_, offset, data.heading);

    // 档位 tap_pos (BYTE)
    MakeUnsignedChar(buffer_, offset, data.tap_pos);

    // 方向盘转角 steering_angle (DWORD)
    MakeUnsignedInt(buffer_, offset, data.steering_angle);

    // 当前车速 velocity (WORD)
    MakeUnsignedShort(buffer_, offset, data.velocity);

    // 纵向加速度 acceleration_lon (WORD)
    MakeUnsignedShort(buffer_, offset, data.acceleration_lon);

    // 横向加速度 acceleration_lat (WORD)
    MakeUnsignedShort(buffer_, offset, data.acceleration_lat);

    // 垂向加速度 acceleration_ver (WORD)
    MakeUnsignedShort(buffer_, offset, data.acceleration_ver);

    // 横摆角速度 yaw_rate (WORD)
    MakeUnsignedShort(buffer_, offset, data.yaw_rate);

    // 油门开度 accel_pos (WORD)
    MakeUnsignedShort(buffer_, offset, data.accel_pos);

    // 发动机输出转速 engine_speed (WORD)
    MakeUnsignedShort(buffer_, offset, data.engine_speed);

    // 发动机扭矩 engine_torque (DWORD)
    MakeUnsignedInt(buffer_, offset, data.engine_torque);

    // 制动踏板开关 brake_flag (BYTE)
    MakeUnsignedChar(buffer_, offset, data.brake_flag);

    // 制动踏板开度 brake_pos (WORD)
    MakeUnsignedShort(buffer_, offset, data.brake_pos);

    // 制动主缸压力 brake_pressure (WORD)
    MakeUnsignedShort(buffer_, offset, data.brake_pressure);

    // 油耗 fule_consumption (WORD)
    MakeUnsignedShort(buffer_, offset, data.fule_consumption);

    // 车辆驾驶模式 drive_mode (BYTE)
    MakeUnsignedChar(buffer_, offset, data.drive_mode);

    // 目的地位置 dest_position (WayPointLL: longitude, latitude)
    MakeUnsignedInt(buffer_, offset, data.dest_position.longitude);
    MakeUnsignedInt(buffer_, offset, data.dest_position.latitude);

    // 途经点数量 pass_points_num (BYTE)
    MakeUnsignedChar(buffer_, offset, data.pass_points_num);

    // 途经点列表 pass_points
    for(const auto& point : data.pass_points)
    {
        MakeUnsignedInt(buffer_, offset, point.longitude);
        MakeUnsignedInt(buffer_, offset, point.latitude);
    }
}