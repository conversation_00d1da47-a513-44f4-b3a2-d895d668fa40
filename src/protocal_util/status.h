/*---------------------------------------------------------------------
/* Copyright (c) 2023 tsari. All rights reserved. */
/**
* @Author: hlf
* @brief: 车辆系统工作状态信息上报协议类
 *       
 * @version: 版本编号  修订日期  修订者 修订内容 （最新的在最前面）
 * --------------------------------------------------------------------
*/
#pragma once
#include "protocal_base.h"
namespace cvis_bcl
{
    class Status:public ProtocalBase
    {
        public:
        Status();
        virtual ~Status();
        virtual bool ParseProtocal(BYTE* buffer,int length){};
        BYTE* MakeStatusPackage(StatusData data,DWORD& length);
    };
}