/*---------------------------------------------------------------------
/* Copyright (c) 2023 tsari. All rights reserved. */
/**
* @Author: hlf
* @brief: 车辆运行状态V3协议类
 *        1. 实现StateV3Data结构体的序列化
 *        2. 支持车辆运行状态信息V3版本的数据打包
 * @version: 版本编号  修订日期  修订者 修订内容 （最新的在最前面）
 * --------------------------------------------------------------------
*/

#pragma once
#include "protocal_base.h"
namespace cvis_bcl
{
    
    class StateV3 : public ProtocalBase
    {
    public:
        StateV3();
        virtual ~StateV3();
        
        virtual bool ParseProtocal(BYTE* buffer, int length) override { return false; };
        
        //组装state报文,发送完成后需要调用Free()函数，释放申请的内存
        BYTE* MakeStateV3Package(StateV3Data data, DWORD& length);
        
        void MakePureDataPackage(BYTE* buffer, DWORD& offset, StateV3Data data);
    };

}
