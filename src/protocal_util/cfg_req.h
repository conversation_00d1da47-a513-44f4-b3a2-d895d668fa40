/*---------------------------------------------------------------------
/* Copyright (c) 2023 tsari. All rights reserved. */
/**
* @Author: hlf
* @brief: 车辆配置请求信息协议类
 *       
 * @version: 版本编号  修订日期  修订者 修订内容 （最新的在最前面）
 * --------------------------------------------------------------------
*/
#pragma once
#include "protocal_base.h"
namespace cvis_bcl
{
    class CfgReq:public ProtocalBase
    {
        public:
        CfgReq();
        virtual ~CfgReq();
        virtual bool ParseProtocal(BYTE* buffer,int length){};
        BYTE* MakeCfgReqPackage(CfgReqData data,DWORD& length);
    };
}