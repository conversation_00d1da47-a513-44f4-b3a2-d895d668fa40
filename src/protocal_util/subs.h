/*---------------------------------------------------------------------
/* Copyright (c) 2023 tsari. All rights reserved. */
/**
* @Author: hlf
* @brief: 车辆功能订阅信息协议类
 *        1. 实现SubsData结构体的序列化
 *        2. 支持车辆功能订阅信息的数据打包
 * @version: 版本编号  修订日期  修订者 修订内容 （最新的在最前面）
 * --------------------------------------------------------------------
*/

#pragma once
#include "protocal_base.h"
namespace cvis_bcl
{
    
    class Subs : public ProtocalBase
    {
    public:
        Subs();
        virtual ~Subs();
        
        virtual bool ParseProtocal(BYTE* buffer, int length) override { return false; };
        
        //组装subs报文,发送完成后需要调用Free()函数，释放申请的内存
        BYTE* MakeSubsPackage(SubsData data, DWORD& length);
        
        void MakePureDataPackage(BYTE* buffer, DWORD& offset, SubsData data);
    };

}
