#include "function_request_res.h"

using namespace cvis_bcl;

FunctionRequestRes::FunctionRequestRes()
{
    msg_type = "C2V_FUNC_REQ_RES";
}

FunctionRequestRes::~FunctionRequestRes()
{

}

bool FunctionRequestRes::ParseProtocal(BYTE* buffer,int length)
{
    DWORD offset=0;
    if(!header_.ParseHeader(buffer,offset,length))
    {
        return false;
    }
    if(header_.GetLength()+offset>length)
    {
        return false;
    }
    data_.msg_seq = GetHostUnsignedInt(buffer,offset);
    for(int i=0;i<8;i++)
    {
        data_.veh_id[i] = GetHostUnsignedChar(buffer,offset); 
    }
    for(int i=0;i<36;i++)
    {
        data_.uuid[i] = GetHostUnsignedChar(buffer,offset); 
    }
    for(int i=0;i<6;i++)
    {
        data_.func_req[i] =GetHostUnsignedChar(buffer,offset);
    }
    return true;
}

FuncReqDataRes FunctionRequestRes::GetData()
{
    return data_;
}

std::vector<std::string> FunctionRequestRes::GetFunReqList()
{
    std::vector<std::string> func_req;
    if(data_.func_req[0]&0x01)
    {
        func_req.push_back(FunReqReverseMap[CLOUD2VEH_ADVICE_GLOSA]);
    }
}