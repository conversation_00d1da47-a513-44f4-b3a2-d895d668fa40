#include "state_resend.h"
#include <string.h>
using namespace cvis_bcl;

StateResend::StateResend()
{
    msg_type = "V2C_STATE_RESEND";
}

StateResend::~StateResend()
{

}

BYTE* StateResend::MakeStateResendPackage(StateResendData data, DWORD& length)
{
    // 使用 StateResendData 的 GetLength 方法计算实际数据长度
    HeaderData header = MakeHeader(msg_type, data.GetLength());
    length = data.GetLength() + sizeof(header);
    buffer_ = (BYTE*)malloc(length);
    memset(buffer_, 0, length);
    uint32_t offset = 0;
    header_.MakeHeader(buffer_, offset, header);
    MakePureDataPackage(buffer_, offset, data);
    return buffer_;
}

void StateResend::MakePureDataPackage(BYTE* buffer, DWORD& offset, StateResendData data)
{
    // 车辆编号 vehicle_id[8]
    for(int i = 0; i < 8; i++)
    {
        MakeUnsignedChar(buffer, offset, data.vehicle_id[i]);
    }
    
    // 补发消息版本号 res_version
    MakeUnsignedChar(buffer, offset, data.res_version);
    
    // 补发消息序号 message_id
    MakeUnsignedShort(buffer, offset, data.message_id);
    
    // 补发数据内容列表 res_content
    for(const auto& content : data.res_content)
    {
        // 补发数据长度 res_length
        MakeUnsignedShort(buffer, offset, content.res_length);
        
        // 补发状态信息 res_content
        for(const auto& byte_data : content.res_content)
        {
            MakeUnsignedChar(buffer, offset, byte_data);
        }
    }
}
