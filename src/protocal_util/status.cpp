#include "status.h"
#include <string.h>

using namespace cvis_bcl;

Status::Status()
{
    msg_type="V2C_Status";
}

Status::~Status()
{

}

BYTE* Status::MakeStatusPackage(StatusData data,DWORD& length)
{
    HeaderData header = MakeHeader(msg_type,sizeof(data));
    length = sizeof(data)+sizeof(header);
    buffer_ = (BYTE*)malloc(length);
    memset(buffer_,0,length);
    uint32_t offset =0;
    header_.MakeHeader(buffer_,offset,header);
     MakeUnsignedInt(buffer_,offset,data.msg_seq);
    for(int i=0;i<8;i++)
    {
        buffer_[offset++]=data.veh_id[i];
    }
    MakeUsignedLongLong(buffer_,offset,data.timestamp);
    buffer_[offset++] = data.network_pc5_valid;
    buffer_[offset++] = data.veh_failure;
    buffer_[offset++] = data.veh_ads_start_failure;
    buffer_[offset++] = data.time_sync_valid;
    buffer_[offset++] = data.ad_valid;
    buffer_[offset++] = data.sensor_valid;
    buffer_[offset++] = data.veh_wired_control_valid;
    buffer_[offset++] = data.veh_hmi_valid;
    buffer_[offset++] = data.dispatch_enable;
    buffer_[offset++] = data.global_route_enable;
    buffer_[offset++] = data.local_route_enable;
    buffer_[offset++] = data.remote_control_enable;
}