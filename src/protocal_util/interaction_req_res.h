/*---------------------------------------------------------------------
/* Copyright (c) 2023 tsari. All rights reserved. */
/**
* @Author: hlf
* @brief: 车辆接管交互请求返回协议类
 *       
 * @version: 版本编号  修订日期  修订者 修订内容 （最新的在最前面）
 * --------------------------------------------------------------------
*/
#pragma once
#include "protocal_base.h"
namespace cvis_bcl
{
    class InteractionReqRes:public ProtocalBase
    {
        public:
        InteractionReqRes();
        virtual ~InteractionReqRes();
        virtual bool ParseProtocal(BYTE* buffer,int length);
        InteractionReqResData GetData();
        private:
        InteractionReqResData data_;
    };

}