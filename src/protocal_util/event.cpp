#include "event.h"
#include "string.h"

using namespace cvis_bcl;

Event::Event()
{
    msg_type="V2C_EVENT";
}

Event::~Event()
{

}

BYTE* Event::MakeEventPackage(EventData data,DWORD& length)
{
    HeaderData header = MakeHeader(msg_type,data.GetLength());
    length = data.GetLength()+sizeof(header);
    
    buffer_ = (BYTE*)malloc(length);
    memset(buffer_,0,length);
    uint32_t offset =0;
    header_.MakeHeader(buffer_,offset,header);
    MakeUnsignedInt(buffer_,offset,data.msg_seq);
    for(int i=0;i<8;i++)
    {
        MakeUnsignedChar(buffer_,offset,data.veh_id[i]);
    }
    MakeUsignedLongLong(buffer_,offset,data.timestamp);
    MakeUnsignedChar(buffer_,offset,data.event_num);
    for(int i=0;i<data.event_num;i++)
    {
        MakeTrafficInfo(buffer_,offset,data.traffic_evvent_info[i]);
    }
    return buffer_;
}

void Event::MakeTrafficInfo(BYTE* buffer,DWORD& offset,TrafficEventInfo data)
{
    for(int i=0;i<36;i++)
    {
        MakeUnsignedChar(buffer,offset,data.event_id[i]);
    }
    MakeUnsignedChar(buffer,offset,data.valid);
    MakeUnsignedChar(buffer,offset,data.alert_type);
    MakeUnsignedChar(buffer,offset,data.event_confidence);
    MakeUnsignedInt(buffer,offset,MakeLongitude(data.longitude));
    MakeUnsignedInt(buffer,offset,MakeLatitude(data.latitude));
    MakeUnsignedInt(buffer,offset,MakeElevation(data.elevation));
    MakeUnsignedChar(buffer,offset,data.content_len);
    for(int i=0;i<data.content_len;i++)
    {
        MakeUnsignedChar(buffer,offset,*(data.content.data()+i));
    }
    MakeUnsignedShort(buffer,offset,MakeWORD(data.alert_radius,0.01,0));
    MakeUnsignedChar(buffer,offset,data.alert_path_point_num);
    for(int i=0;i<data.alert_path_point_num;i++)
    {
        MakeUnsignedInt(buffer,offset,MakeLongitude(data.alert_path_points[i].longitude));
        MakeUnsignedInt(buffer,offset,MakeLatitude(data.alert_path_points[i].latitude));
        MakeUnsignedInt(buffer,offset,MakeElevation(data.alert_path_points[i].elevation));
    }
}