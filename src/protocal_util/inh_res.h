/*---------------------------------------------------------------------
/* Copyright (c) 2023 tsari. All rights reserved. */
/**
* @Author: hlf
* @brief: 车辆准静态参数信息返回协议类
 *        1. 实现INHResData结构体的序列化
 *        2. 支持车辆准静态参数信息返回的数据打包
 * @version: 版本编号  修订日期  修订者 修订内容 （最新的在最前面）
 * --------------------------------------------------------------------
*/

#pragma once
#include "protocal_base.h"
namespace cvis_bcl
{
    class INHRes : public ProtocalBase
    {
    public:
        INHRes();
        virtual ~INHRes();

        virtual bool ParseProtocal(BYTE* buffer, int length) override;

        //组装INH响应报文,发送完成后需要调用Free()函数，释放申请的内存
        BYTE* MakeINHResPackage(INHResData data, DWORD& length);

        void MakePureDataPackage(BYTE* buffer, DWORD& offset, INHResData data);

        INHResData GetData();

    private:
        INHResData data_;
    };
}