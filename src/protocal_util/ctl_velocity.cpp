#include "ctl_velocity.h"

using namespace cvis_bcl;

CtlVelocity::CtlVelocity()
{
    msg_type ="C2V_CTL_VELOCITY";
}

CtlVelocity::~CtlVelocity()
{

}

bool CtlVelocity::ParseProtocal(BYTE* buffer,int length)
{
    DWORD offset=0;
    if(!header_.ParseHeader(buffer,offset,length))
    {
        return false;
    }
    if(header_.GetLength()+offset>length)
    {
        return false;
    }
    data_.msg_seq = GetHostUnsignedInt(buffer,offset);
    for(int i=0;i<8;i++)
    {
        data_.veh_id[i] = GetHostUnsignedChar(buffer,offset);
    }
    data_.timestamp = GetHostlonglong(buffer,offset);
    data_.auto_drive_mode_switch = GetHostUnsignedChar(buffer,offset);
    data_.advice_velocity = GetValue<float,WORD>(GetHostUnsignedShort(buffer,offset),0.01,0);
    data_.advice_acceleration = GetValue<float,WORD>(GetHostUnsignedShort(buffer,offset),0.01,-10000);
    data_.way_point_num = GetHostUnsignedChar(buffer,offset);
    for(int i=0;i<data_.way_point_num;i++)
    {
        WayPointLLEV point;
        ParseWayPointLLEV(buffer,offset,point);
        data_.way_points.push_back(point);
    }
}

void CtlVelocity::ParseWayPointLLEV(BYTE* buffer,DWORD& offset,WayPointLLEV& point)
{
    point.longitude = GetLongitude(GetHostUnsignedInt(buffer,offset));
    point.latitude = GetLatitude(GetHostUnsignedInt(buffer,offset));
    point.elevation = GetElevation(GetHostUnsignedInt(buffer,offset));
    point.velocity = GetValue<float,WORD>(GetHostUnsignedShort(buffer,offset),0.01,0);
}

CtlVelocityData CtlVelocity::GetData()
{
    return data_;
}