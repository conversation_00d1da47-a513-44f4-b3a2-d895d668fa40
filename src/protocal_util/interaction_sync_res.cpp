#include "interaction_sync_res.h"
#include "string.h"

using namespace cvis_bcl;

InteractionSyncRes::InteractionSyncRes()
{
    msg_type ="V2C_INTERACTION_SYNC_RES";
}

InteractionSyncRes::~InteractionSyncRes()
{

}

BYTE* InteractionSyncRes::MakeProtocalPackage(InteractionSyncResData data,DWORD& length)
{
    HeaderData header = MakeHeader(msg_type,data.GetLength());
    length = data.GetLength()+sizeof(header);
    buffer_ = (BYTE*)malloc(length);
    memset(buffer_,0,length);
    uint32_t offset =0;
    header_.MakeHeader(buffer_,offset,header);
    MakeUnsignedInt(buffer_,offset,data.msg_seq);
    for(int i=0;i<8;i++)
    {
       MakeUnsignedChar(buffer_,offset,data.veh_id[i]);
    }
    for(int i=0;i<36;i++)
    {
       MakeUnsignedChar(buffer_,offset,data.uuid[i]);
    }
    MakeUnsignedChar(buffer_,offset,data.do_flag);
    MakeUnsignedChar(buffer_,offset,data.error_code);
    MakeUnsignedChar(buffer_,offset,data.content_len);
    for(int i=0;i<data.content_len;i++)
    {
        MakeUnsignedChar(buffer_,offset,*(data.content.data()+i));
    }
    return buffer_;
}