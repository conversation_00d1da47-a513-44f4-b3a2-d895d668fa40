/*---------------------------------------------------------------------
/* Copyright (c) 2023 tsari. All rights reserved. */
/**
* @Author: hlf
* @brief: 消息映射表
 *
 * @version: 版本编号  修订日期  修订者 修订内容 （最新的在最前面）
 * --------------------------------------------------------------------
*/
#pragma once
#include <unordered_map>
#include <string>
namespace cvis_bcl
{
    static std::unordered_map<int,std::string> topic_reflect_map=
    {
        {0x0C,"HEARTBEAT"},
        {0x0D,"HEARTBEAT_RES"},
        {0x15,"V2C_STATE"},
        {0x1E,"C2V_INSTRUCT"},
        {0x1F,"C2V_REMOTECTL"},
        {0x34,"V2C_INH"},
        {0x35,"C2V_INH_RES"},
        {0x36,"V2C_SUBS"},
        {0x37,"C2V_SUBS_RES"},
        {0x3C,"C2V_ADVICE"},
        {0x3D,"V2C_ADVICE_RES"},
        {0x3E,"C2V_ASTFUNC"},
        {0x3F,"V2C_ASTFUNC_RES"},
        {0x5C,"V2C_TEST"},
        {0x5D,"C2V_TEST_RES"},
        {0x5E,"C2V_TEST"},
        {0x5F,"V2C_TEST_RES"},
        {0x60,"V2C_STATE_RESEND"},
        {0x61,"C2V_STATE_RESEND_RES"},

        //rcu
        {0x79,"RCU2CLOUD_OBJS"},
        {0x7B,"RCU2CLOUD_EVENT"},
        {0x7C,"CLOUD2RCU_EVENT_RES"},
        {0x7D,"RCU2CLOUD_EVENT_CANCEL"},
        {0x7E,"CLOUD2RCU_EVENT_CANCEL_RES"},
        {0x81,"RCU2CLOUD_STATUS"},
        {0x82,"CLOUD2RCU_STATUS_RES"},
        {0x8D,"RCU2CLOUD_HEARTBEAT"},
        {0x8E,"CLOUD2RCU_HEARTBEAT_RES"}
    };

    static std::unordered_map<std::string,int> datatype_reflect_map=
    {
        {"HEARTBEAT",0x0C},
        {"HEARTBEAT_RES",0x0D},
        {"V2C_STATE",0x15},
        {"C2V_INSTRUCT", 0x1E},
        {"C2V_REMOTECTL",0x1F},
        {"V2C_INH",0x34},
        {"C2V_INH_RES",0x35},
        {"V2C_SUBS",0x36},
        {"C2V_SUBS_RES",0x37},
        {"C2V_ADVICE",0x3C},
        {"V2C_ADVICE_RES",0x3D},
        {"C2V_ASTFUNC",0x3E},
        {"V2C_ASTFUNC_RES",0x3F},
        {"V2C_TEST",0x5C},
        {"C2V_TEST_RES",0x5D},
        {"C2V_TEST",0x5E},
        {"V2C_TEST_RES",0x5F},
        {"V2C_STATE_RESEND",0x60},
        {"C2V_STATE_RESEND_RES",0x61},

        //rcu
        {"RCU2CLOUD_OBJS",0x79},
        {"RCU2CLOUD_EVENT",0x7B},
        {"CLOUD2RCU_EVENT_RES",0x7C},
        {"RCU2CLOUD_EVENT_CANCEL",0x7D},
        {"CLOUD2RCU_EVENT_CANCEL_RES",0x7E},
        {"RCU2CLOUD_STATUS",0x81},
        {"CLOUD2RCU_STATUS_RES",0x82},
        {"RCU2CLOUD_HEARTBEAT",0x8D},
        {"CLOUD2RCU_HEARTBEAT_RES",0x8E}

    };
}
