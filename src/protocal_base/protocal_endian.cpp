#include "protocal_endian.h"
#include <iostream>
using namespace cvis_bcl;

ProtocalEndian::ProtocalEndian()
{
    byte_order_ = GetByteOrder();
}

ProtocalEndian::~ProtocalEndian()
{

}

ByteOrder ProtocalEndian::GetByteOrder()
{
    int temp_i =0;
    char* temp_p =nullptr;
    temp_p = (char*)&temp_i;
    temp_p[0] = 0;
    temp_p[1] = 0;
    temp_p[2] = 0;
    temp_p[3] = 1;
    if(temp_i == 1)
    {
        return CVIS_BIG_ENDIAN;
    }
    return CVIS_LITTLE_ENDIAN;
}

BYTE ProtocalEndian::GetBit2Byte(BYTE* buffer,DWORD& offset)
{
    BYTE value =0;
    value = buffer[offset++];

   
    return value;
}


BYTE ProtocalEndian::GetHostUnsignedChar(BYTE* buffer,DWORD& offset)
{
    unsigned char value = buffer[offset];
    offset++;
    return value;
}


WORD ProtocalEndian::GetHostUnsignedShort(BYTE* buffer,DWORD& offset)
{
    unsigned short value=0;
    BYTE tmp;
    tmp = buffer[offset];
        value += (unsigned short)((buffer[offset++]&0xFF)<<8);
        value += (unsigned short)(buffer[offset++]&0xFF);
    return value;
}


DWORD ProtocalEndian::GetHostUnsignedInt(BYTE* buffer,DWORD& offset)
{
    unsigned int value =0;
    for(int i=0;i<4;i++)
    {
        value += (unsigned int)(buffer[offset++]&0xFF)<<((3-i)*8);
    }
    
    return value;
}

TIMESTAMP ProtocalEndian::GetHostlonglong(BYTE* buffer,DWORD& offset)
{
     TIMESTAMP value =0;
     for(int i=0;i<8;i++)
    {
        value += (TIMESTAMP)(buffer[offset++]&0xFF)<<((7-i)*8);
    }
    return value;
}

void ProtocalEndian::MakeBitByte(BYTE* buffer,DWORD& offset,BYTE value)
{

        buffer[offset++] = value&0xFF;
    
    
}

void ProtocalEndian::MakeUnsignedChar(BYTE* buffer,DWORD& offset,BYTE value)
{
    buffer[offset++] = value&0xFF;
}



void ProtocalEndian::MakeUnsignedShort(BYTE* buffer,DWORD& offset,WORD value)
{
    std::cout<<"WORD "<<offset<<std::endl;
    buffer[offset++] = (BYTE)((value>>8)&0xFF);
    buffer[offset++] = (BYTE)(value&0xFF);
    
}

void ProtocalEndian::MakeUnsignedInt(BYTE* buffer,DWORD& offset,DWORD value)
{
    std::cout<<"DWORD "<<offset<<std::endl;
    for(int i=0;i<4;i++)
    {
         buffer[offset++] = (BYTE)((value>>((3-i)*8))&0xFF);
    }
       
}

void ProtocalEndian::MakeUsignedLongLong(BYTE* buffer,DWORD& offset,uint64_t value)
{
    std::cout<<"uint64_ "<<offset<<std::endl;
    for(int i=0;i<8;i++)
    {
         buffer[offset++] = (BYTE)((value>>((7-i)*8))&0xFF);
    }
}