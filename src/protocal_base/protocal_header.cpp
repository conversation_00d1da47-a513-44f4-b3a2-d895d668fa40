#include "protocal_header.h"

using namespace cvis_bcl;

Header::Header()
{

}

Header::~Header()
{

}

bool Header::ParseHeader(BYTE* buffer,unsigned int& offset,int length)
{
    if(buffer[offset]!=0xF2||offset+16>=length)
    {
        return false;
    }
    offset++;
    data_.length = GetHostUnsignedInt(buffer,offset);
    data_.msg_type = GetHostUnsignedChar(buffer,offset);
    data_.version = GetHostUnsignedChar(buffer,offset);
    data_.timestamp = GetHostlonglong(buffer,offset);
    data_.control = GetHostUnsignedChar(buffer,offset);
    return true;
}

void Header::MakeHeader(BYTE* buffer,DWORD& offset,HeaderData data)
{
    MakeUnsignedChar(buffer,offset,0xF2);
    MakeUnsignedInt(buffer,offset,data.length);
    MakeUnsignedChar(buffer,offset,data.msg_type&0xFF);
    MakeUnsignedChar(buffer,offset,data.version&0xFF);
    MakeUsignedLong<PERSON>ong(buffer,offset,data.timestamp);
    MakeUnsignedChar(buffer,offset,0&0xFF);
}


DWORD Header::GetLength()
{
    return  data_.length;
}

BYTE Header::GetMsgType()
{
    return  data_.msg_type;
}

BYTE Header::GetVersion()
{
    return  data_.version;
}

TIMESTAMP Header::GetTimestamp()
{
    return  data_.timestamp;
}

BYTE Header::GetControl()
{
    return  data_.control;
}

void Header::SetMsgType(BYTE msg_type)
{
    data_.msg_type =msg_type;
}

void Header::SetLength(DWORD length)
{
     data_.length = length;
}

void Header::SetVersion(BYTE version)
{
     data_.version = version;
}

void Header::SetTimestamp(TIMESTAMP timestamp)
{
     data_.timestamp = timestamp;
}

void Header::SetControl(BYTE control)
{
     data_.control = control;
}