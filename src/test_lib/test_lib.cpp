#include "protocal_endian.h"
#include <iostream>
#include "protocal_map.hpp"
#include "heartbeat.h"
int main(int argc,char** argv)
{
    cvis_bcl::ProtocalEndian base;
    if(base.GetByteOrder()==cvis_bcl::CVIS_LITTLE_ENDIAN)
    {
        std::cout<<"host is littile_endian"<<std::endl;
    }
    else
    {
        std::cout<<"host is big_endian"<<std::endl;
    }
     DWORD temp_i =0;
    BYTE temp_p[4];
    temp_p[0] = 0;
    temp_p[1] = 0;
    temp_p[2] = 1;
    temp_p[3] = 1;
    unsigned int offset =0;
    DWORD out = base.GetHostUnsignedInt(temp_p,offset);
    std::cout<<"current value "<<out<<" "<<offset<< std::endl;
    std::cout<<"current topic "<<cvis_bcl::topic_reflect_map[99]<<std::endl;

    WORD test_short = 255;
    BYTE temp[2];
    offset =0;
    temp[offset] = (char)(test_short&0xFF);
    temp[++offset] = (char)((test_short>>8)&0xFF);
    
    std::cout<<"temp[0] "<<(int)temp[0]<<" temp[1] "<<(int)temp[1]<<" "<<offset<<std::endl;
    cvis_bcl::HeartBeat heartbeat;
    cvis_bcl::HeartBeatData data;
    data.msg_seq =1;
    data.timestamp=1;
    heartbeat.ConvertString2Byte("12341234",data.veh_id,8);
    
    DWORD length;
    BYTE* buffer = heartbeat.MakeHeartBeatPackage(data,length);
    
    for(int i=0;i<length;i++)
    {
        std::cout<<(int)buffer[i]<<" ";
    }
    std::cout<<std::endl;
    heartbeat.ParseProtocal(buffer,length);
    std::cout<<"vehid "<<heartbeat.GetVehId()<<std::endl;
    heartbeat.Free();
    cvis_bcl::FuncReqData funcreq;
    std::cout<<"size of "<<funcreq.GetLength()<<std::endl;
    cvis_bcl::StateV1Data state_v1;
    
}