cmake_minimum_required(VERSION 3.0.2)
set(CMAKE_BUILD_TYPE "Debug")
project(terminal_cloud_app)
# include(${PROJECT_SOURCE_DIR}/version.cmake)

# 根据git tag动态设置版本号
# EXECUTE_PROCESS(COMMAND git describe --tags
#         TIMEOUT 10
#         OUTPUT_VARIABLE GIT_VERSION
#         OUTPUT_STRIP_TRAILING_WHITESPACE
#         )
# MESSAGE(STATUS "building from git tag ${GIT_VERSION}")
# add_definitions(-DBUILD_VERSION=\"${GIT_VERSION}\")

#外部用cmake . -DBUILD_PLATFORM=arm64进行值传入，便可以执行不同的逻辑
SET(BUILD_PLATFORM "x86_64" CACHE STRING "select build cpu type")
if (BUILD_PLATFORM STREQUAL arm64)
 set(tool_path /usr/local/toolchain/gcc-linaro-7.4.1-2019.02-x86_64_aarch64-linux-gnu)
 
 set(CMAKE_C_COMPILER ${tool_path}/bin/aarch64-linux-gnu-gcc)
 set(CMAKE_CXX_COMPILER ${tool_path}/bin/aarch64-linux-gnu-g++)
 
 set(CMAKE_CROSSCOMPILING TRUE)
 set(CMAKE_SYSTEM_NAME "Linux")
 set(CMAKE_SYSTEM_PROCESSOR arm)

 set(CMAKE_FIND_ROOT_PATH ${tool_path})
 set(CMAKE_FIND_ROOT_PATH_MODE_LIBRARY ONLY)

 set(LIBRARY_OUTPUT_PATH ${PROJECT_SOURCE_DIR}/lib/arm64)
 set(target_dir target_arm64)

 include_directories(${PROJECT_SOURCE_DIR}/include/openssl_1.0.2)
 link_directories(${PROJECT_SOURCE_DIR}/lib/arm64)
 link_directories(${PROJECT_SOURCE_DIR}/lib/arm64/thirdlib)

 message("-- this is arm64 platform")
else()
 set(LIBRARY_OUTPUT_PATH ${PROJECT_SOURCE_DIR}/lib/x86)
 set(target_dir target_x86)

 include_directories(${PROJECT_SOURCE_DIR}/include/openssl_1.1.1)
 link_directories(${PROJECT_SOURCE_DIR}/lib/x86)
 link_directories(${PROJECT_SOURCE_DIR}/lib/x86/thirdlib)

 message("-- this is x86_64 platform")
endif()

message("-- build type ${CMAKE_BUILD_TYPE}")

ADD_DEFINITIONS(-std=c++14)

set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} -fPIC -ldl -pthread")
set(CMAKE_INSTALL_RPATH "$ORIGIN/../lib")

if(NOT EXISTS ${PROJECT_SOURCE_DIR}/bin)
    file(MAKE_DIRECTORY ${PROJECT_SOURCE_DIR}/bin)
endif()
set(EXECUTABLE_OUTPUT_PATH ${PROJECT_SOURCE_DIR}/bin)

include_directories(${PROJECT_SOURCE_DIR}/src)
include_directories(${PROJECT_SOURCE_DIR}/include)
include_directories(${PROJECT_SOURCE_DIR}/include/common/logger)


set(CMAKE_INSTALL_INCLUDE_PATH ${CMAKE_SOURCE_DIR}/include)
message(" include path ${CMAKE_INSTALL_INCLUDE_PATH}")

add_subdirectory(src)



install(TARGETS  DESTINATION ${PROJECT_SOURCE_DIR}/${target_dir}/bin)
install(DIRECTORY ${LIBRARY_OUTPUT_PATH}/ DESTINATION ${PROJECT_SOURCE_DIR}/${target_dir}/lib)
# install(DIRECTORY ${PROJECT_SOURCE_DIR}/conf DESTINATION ${PROJECT_SOURCE_DIR}/${target_dir})


# install(DIRECTORY ${PROJECT_SOURCE_DIR}/startshell/  DESTINATION ${PROJECT_SOURCE_DIR}/${target_dir})